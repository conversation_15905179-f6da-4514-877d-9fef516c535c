spring:
  cloud:
    sentinel:
      transport:
        dashboard: ${nft.turbo.sentinel.url}
        port: ${nft.turbo.sentinel.port}
      datasource:
        ds:
          nacos:
            server-addr: ${nft.turbo.nacos.server.url}
            data-id:  ${nft.turbo.sentinel.nacos.data-id}
            group-id: DEFAULT_GROUP
            data-type: json
            rule-type: flow  # 也可以是 degrade, param-flow, etc.