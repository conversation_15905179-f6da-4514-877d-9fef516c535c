seata:
  application-id: ${spring.application.name}
  tx-service-group: default_tx_group
#  use-jdk-proxy: true
#  enable-auto-data-source-proxy: false
  config:
    type: nacos
    nacos:
      server-addr: ${nft.turbo.nacos.server.url}
      group: ${nft.turbo.seata.nacos.group}
      data-id: ${nft.turbo.seata.nacos.data-id}
      namespace: ${nft.turbo.seata.nacos.namespace}
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: ${nft.turbo.nacos.server.url}
      group: ${nft.turbo.seata.nacos.group}
      cluster: default
      namespace: ${nft.turbo.seata.nacos.namespace}