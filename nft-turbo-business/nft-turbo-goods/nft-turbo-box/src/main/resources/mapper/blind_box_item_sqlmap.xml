<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.hollis.nft.turbo.box.infrastructure.mapper.BlindBoxItemMapper">

    <resultMap id="resultBlindBoxItemMap" type="cn.hollis.nft.turbo.box.domain.entity.BlindBoxItem">
        <result property="id" column="ID"/>
        <result property="gmtCreate" column="GMT_CREATE"/>
        <result property="gmtModified" column="GMT_MODIFIED"/>
        <result property="openedTime" column="OPENED_TIME"/>
        <result property="assignTime" column="ASSIGN_TIME"/>
        <result property="blindBoxId" column="BLIND_BOX_ID"/>
        <result property="name" column="NAME"/>
        <result property="cover" column="COVER"/>
        <result property="detail" column="DETAIL"/>
        <result property="collectionName" column="COLLECTION_NAME"/>
        <result property="collectionCover" column="COLLECTION_COVER"/>
        <result property="collectionDetail" column="COLLECTION_DETAIL"/>
        <result property="collectionSerialNo" column="COLLECTION_SERIAL_NO"/>
        <result property="state" column="STATE"/>
        <result property="userId" column="USER_ID"/>
        <result property="purchasePrice" column="PURCHASE_PRICE"/>
        <result property="referencePrice" column="REFERENCE_PRICE"/>
        <result property="rarity" column="rarity"/>
        <result property="purchasePrice" column="PURCHASE_PRICE"/>
        <result property="orderId" column="ORDER_ID"/>
        <result property="deleted" column="DELETED"/>
        <result property="lockVersion" column="LOCK_VERSION"/>
    </resultMap>

    <select id="queryRandomByBoxIdAndState" resultType="java.lang.Long">
        SELECT id FROM blind_box_item
        WHERE blind_box_id = #{blindBoxId} AND state = #{state}
        ORDER BY RAND() LIMIT 1
    </select>

</mapper>