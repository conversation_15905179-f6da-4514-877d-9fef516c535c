<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.hollis</groupId>
        <artifactId>nft-turbo-business</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>cn.hollis</groupId>
    <artifactId>nft-turbo-app</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <application.name>nfturbo-business</application.name>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>
        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-chain</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-collection</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-box</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-goods-interface</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-inventory</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-notice</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-pay</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-order</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-tcc</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-user</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-trade</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-prometheus</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-skywalking</artifactId>
        </dependency>

        <!--    shardingsphere和2.2不兼容，需要使用1.33，但是1.33和springboot 3.2.2 不兼容，所以自定义了 TagInspector和 UnTrustedTagInspector  -->
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.33</version>
        </dependency>

    </dependencies>
</project>