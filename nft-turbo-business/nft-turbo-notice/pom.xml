<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.hollis</groupId>
        <artifactId>nft-turbo-business</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>cn.hollis</groupId>
    <artifactId>nft-turbo-notice</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <application.name>nfturbo-notice</application.name>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-base</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-limiter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-lock</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-rpc</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-config</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-job</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-skywalking</artifactId>
        </dependency>

        <!--    spring-web    -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>6.1.3</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>