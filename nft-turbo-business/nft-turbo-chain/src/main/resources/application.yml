spring:
  profiles:
    active: prod
  application:
    name: @application.name@
  config:
    import: classpath:base.yml,classpath:datasource.yml,classpath:rpc.yml,classpath:cache.yml,classpath:stream.yml  #这里爆红可以直接忽略，没有任何影响
  cloud:
    function:
      definition: chain
    stream:
      bindings:
        chain-out-0:
          content-type: application/json
          destination: chain-result-topic
          group: chain-group
          binder: rocketmq

server:
  port: 8090

