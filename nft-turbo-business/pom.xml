<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <parent>
        <groupId>cn.hollis</groupId>
        <artifactId>NFTurbo</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>cn.hollis</groupId>
    <artifactId>nft-turbo-business</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <application.name>nfturbo-business</application.name>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <module>nft-turbo-notice</module>
        <module>nft-turbo-chain</module>
        <module>nft-turbo-pay</module>
        <module>nft-turbo-app</module>
        <module>nft-turbo-order</module>
        <module>nft-turbo-user</module>
        <module>nft-turbo-trade</module>
        <module>nft-turbo-goods</module>
        <module>nft-turbo-inventory</module>
    </modules>

</project>