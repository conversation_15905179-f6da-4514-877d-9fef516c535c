#è¿æ¯ä¸ä»½ nacos çéç½®æä»¶åå®¹ï¼éç½®æä»¶å(nacos ç data-id)å¿é¡»ä¸º nfturbo-user.propertiesï¼ä¸é¢æ¶åå°çåæ°è¯·æ¹æä½ èªå·±ç
#å¦æä½ æ¯éè¿ app å¯å¨çæ´ä¸ª businessï¼åä¸éè¦è¿ä¸ªæä»¶ï¼åªéè¦nfturbo-business.propertieså°±è¡äº
# æèä½ å¦æä½ ä¸æ³éç½®å° nacos ä¸ï¼ä¹å¯ä»¥æä»éç½®å°ä½ èªå·±ç application.yml ä¸­
spring.auth.host=
spring.auth.path=
spring.auth.appcode=
spring.oss.enabled=true
spring.oss.bucket=
spring.oss.endPoint=https://oss-cn-hangzhou.aliyuncs.com
spring.oss.accessKey=
spring.oss.accessSecret=