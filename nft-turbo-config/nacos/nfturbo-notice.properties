#è¿æ¯ä¸ä»½ nacos çéç½®æä»¶åå®¹ï¼éç½®æä»¶å(nacos ç data-id)å¿é¡»ä¸º nfturbo-notice.propertiesï¼ä¸é¢æ¶åå°çåæ°è¯·æ¹æä½ èªå·±ç
#å¦æä½ æ¯éè¿ app å¯å¨çæ´ä¸ª businessï¼åä¸éè¦è¿ä¸ªæä»¶ï¼åªéè¦nfturbo-business.propertieså°±è¡äº
# æèä½ å¦æä½ ä¸æ³éç½®å° nacos ä¸ï¼ä¹å¯ä»¥æä»éç½®å°ä½ èªå·±ç application.yml ä¸­
spring.sms.enabled=true
spring.sms.host=
spring.sms.path=
spring.sms.appcode=
spring.sms.smsSignId=
spring.sms.templateId=