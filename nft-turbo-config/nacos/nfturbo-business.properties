#è¿æ¯ä¸ä»½ nacos çéç½®æä»¶åå®¹ï¼éç½®æä»¶å(nacos ç data-id)å¿é¡»ä¸º nfturbo-business.propertiesï¼ä¸é¢æ¶åå°çåæ°è¯·æ¹æä½ èªå·±ç
# æèä½ å¦æä½ ä¸æ³éç½®å° nacos ä¸ï¼ä¹å¯ä»¥æä»éç½®å°ä½ èªå·±ç application.yml ä¸­
nft.turbo.chain.wenchang.host=
nft.turbo.chain.wenchang.apiKey=
nft.turbo.chain.wenchang.apiSecret=
nft.turbo.chain.wenchang.chainAddrSuper=
spring.sms.enabled=true
spring.sms.host=
spring.sms.path=
spring.sms.appcode=
spring.sms.smsSignId=
spring.sms.templateId=
nft.turbo.chain.type=WEN_CHANG
spring.auth.host=
spring.auth.path=
spring.auth.appcode=
spring.oss.enabled=true
spring.oss.bucket=
spring.oss.endPoint=https://oss-cn-hangzhou.aliyuncs.com
spring.oss.accessKey=
spring.oss.accessSecret=