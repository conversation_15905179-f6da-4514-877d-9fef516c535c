spring:
  application:
    name: @application.name@
  config:
    import: classpath:base.yml,classpath:cache.yml,classpath:config.yml,classpath:limiter.yml
  cloud:
    gateway:
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Origin, RETAIN_UNIQUE
      globalcors:
        cors-configurations:
          '[/**]':
            allowedHeaders: '*'
            allowedMethods: '*'
            allowedOrigins: '*'
      routes:
        - id: nfturbo-auth
          uri: lb://nfturbo-auth
          predicates:
            - Path=/auth/**,/token/**
        - id: nfturbo-business
          uri: lb://nfturbo-business
          predicates:
            - Path=/trade/**,/order/**,/user/**,/collection/**,/wxPay/**,/box/**
#        - id: nfturbo-collection
#          uri: lb://nfturbo-collection
#          predicates:
#            - Path=/collection/**
#        - id: nfturbo-order
#          uri: lb://nfturbo-order
#          predicates:
#            - Path=/order/**
#        - id: nfturbo-trade
#          uri: lb://nfturbo-trade
#          predicates:
#            - Path=/trade/**
#        - id: nfturbo-user
#          uri: lb://nfturbo-user
#          predicates:
#            - Path=/user/**
#        - id: nfturbo-pay
#          uri: lb://nfturbo-pay
#          predicates:
#            - Path=/wxPay/**
server:
  port: 8081
