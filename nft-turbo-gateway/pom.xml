<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.hollis</groupId>
        <artifactId>NFTurbo</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>cn.hollis</groupId>
    <artifactId>nft-turbo-gateway</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <application.name>nfturbo-gateway</application.name>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!--  限流组件  -->
        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-limiter</artifactId>
            <exclusions>
            <!--  排除spring-web，gateway应用中不能包含spring mvc组件-->
                <exclusion>
                    <groupId> org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId> org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-config</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hollis</groupId>
            <artifactId>nft-turbo-skywalking</artifactId>
        </dependency>

        <!-- Sa-Token 权限认证 -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-reactor-spring-boot3-starter</artifactId>
            <version>1.37.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-jackson</artifactId>
            <version>1.37.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <!--Spring Cloud Loadbalancer-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <!--Spring Cloud Gateway-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>

        <!--    sensitive    -->
        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>sensitive-logback</artifactId>
            <version>1.7.0</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
<!--                    <skip>true</skip>-->
                    <mainClass>cn.hollis.nft.turbo.gateway.NfTurboGatewayApplication</mainClass>
                </configuration>

                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>