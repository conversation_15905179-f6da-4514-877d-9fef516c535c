<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.hollis</groupId>
    <artifactId>NFTurbo</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>NFTurbo</name>
    <packaging>pom</packaging>
    <description>A NFT Turbo</description>

    <properties>
        <java.version>21</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>21</java.version>
        <spring-boot.version>3.2.2</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.1.2</spring-cloud-alibaba.version>
        <dubbo.version>3.2.10</dubbo.version>
    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.2</version>
        <relativePath/>
    </parent>

    <modules>
        <module>nft-turbo-common</module>
        <module>nft-turbo-auth</module>
        <module>nft-turbo-gateway</module>
        <module>nft-turbo-business</module>
        <module>nft-turbo-admin</module>
        <module>nft-turbo-check</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>5.3.0</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>6.1.3</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.30</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-base</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-auth</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-gateway</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-admin</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-notice</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-chain</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-cache</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-sa-token</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-tcc</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-limiter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-datasource</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-es</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-rpc</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-mq</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-lock</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-file</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-sms</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-config</artifactId>
                <version>${project.version}</version>
            </dependency>


            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-collection</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-box</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-inventory</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-goods-interface</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-goods</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-pay</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-order</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-user</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-trade</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-job</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-order-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-seata</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-prometheus</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hollis</groupId>
                <artifactId>nft-turbo-skywalking</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>2.0.42</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.1.3-jre</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>1.6.0.Beta1</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.30</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.5.5.Final</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>*/**</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
